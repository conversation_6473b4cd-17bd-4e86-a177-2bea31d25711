#!/usr/bin/env python3
"""
Test script to verify Socket.IO configuration
"""

import os
import sys
from flask import Flask
from extensions import socketio

def test_socketio():
    """Test Socket.IO configuration"""
    print("Testing Socket.IO configuration...")
    
    # Create a minimal Flask app
    app = Flask(__name__)
    app.config['SECRET_KEY'] = 'test-key'
    
    # Initialize Socket.IO
    socketio.init_app(app)
    
    @app.route('/')
    def index():
        return '<h1>Socket.IO Test</h1><script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script><script>const socket = io(); socket.on("connect", () => console.log("Connected!"));</script>'
    
    @socketio.on('connect')
    def handle_connect():
        print('Client connected')
    
    @socketio.on('disconnect')
    def handle_disconnect():
        print('Client disconnected')
    
    print("✓ Socket.IO configuration successful")
    print("✓ Starting test server on http://127.0.0.1:5002")
    print("✓ Open the URL in browser to test Socket.IO connection")
    
    try:
        socketio.run(app, host='127.0.0.1', port=5002, debug=True)
    except Exception as e:
        print(f"✗ Error: {e}")
        return False
    
    return True

if __name__ == '__main__':
    test_socketio()
